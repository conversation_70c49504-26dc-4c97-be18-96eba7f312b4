.taskbar {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 28px;
  background: #c0c0c0;
  border-top: 1px solid #ffffff;
  border-bottom: 1px solid #808080;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-family: 'MS Sans Serif', sans-serif;
  font-size: 11px;
  z-index: 1000;
  box-shadow: 0 -1px 2px rgba(0, 0, 0, 0.1);
}

.taskbar-left {
  display: flex;
  align-items: center;
  height: 100%;
  flex: 1;
  overflow: hidden;
}

.start-button {
  height: 22px;
  background: #c0c0c0;
  border: 1px outset #c0c0c0;
  font-family: 'MS Sans Serif', sans-serif;
  font-size: 11px;
  font-weight: bold;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 0 8px;
  margin: 2px;
  color: #000;
  user-select: none;
}

.start-button:hover {
  background: #d0d0d0;
}

.start-button:active,
.start-button.active {
  border: 1px inset #c0c0c0;
  background: #a0a0a0;
}

.start-icon {
  font-size: 12px;
  line-height: 1;
}

.start-text {
  font-weight: bold;
}

.taskbar-separator {
  width: 1px;
  height: 18px;
  background: #808080;
  border-right: 1px solid #ffffff;
  margin: 0 2px;
}

.taskbar-windows {
  display: flex;
  align-items: center;
  gap: 2px;
  height: 100%;
  overflow-x: auto;
  flex: 1;
  padding: 0 4px;
}

.taskbar-window {
  height: 22px;
  min-width: 120px;
  max-width: 160px;
  background: #c0c0c0;
  border: 1px outset #c0c0c0;
  font-family: 'MS Sans Serif', sans-serif;
  font-size: 11px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 0 6px;
  color: #000;
  user-select: none;
  overflow: hidden;
}

.taskbar-window:hover {
  background: #d0d0d0;
}

.taskbar-window.active {
  border: 1px inset #c0c0c0;
  background: #a0a0a0;
}

.taskbar-window.minimized {
  background: #e0e0e0;
  color: #606060;
}

.taskbar-window-icon {
  font-size: 12px;
  line-height: 1;
  flex-shrink: 0;
}

.taskbar-window-title {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
}

.taskbar-right {
  display: flex;
  align-items: center;
  height: 100%;
  gap: 4px;
  padding-right: 4px;
}

.system-tray {
  display: flex;
  align-items: center;
  gap: 2px;
  height: 100%;
}

.tray-icon {
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  cursor: pointer;
  border: 1px solid transparent;
  background: transparent;
}

.tray-icon:hover {
  border: 1px outset #c0c0c0;
  background: #d0d0d0;
}

.taskbar-clock {
  background: #c0c0c0;
  border: 1px inset #c0c0c0;
  padding: 2px 6px;
  font-size: 11px;
  color: #000;
  cursor: pointer;
  user-select: none;
  min-width: 60px;
  text-align: center;
}

.taskbar-clock:hover {
  background: #d0d0d0;
}

/* Start Menu */
.start-menu-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1001;
  background: transparent;
}

.start-menu {
  position: fixed;
  bottom: 28px;
  left: 2px;
  width: 200px;
  background: #c0c0c0;
  border: 2px outset #c0c0c0;
  box-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  z-index: 1002;
  font-family: 'MS Sans Serif', sans-serif;
  font-size: 11px;
}

.start-menu-header {
  background: linear-gradient(90deg, #000080 0%, #0000ff 100%);
  color: white;
  padding: 4px;
  border-bottom: 1px solid #808080;
}

.start-menu-banner {
  writing-mode: vertical-rl;
  text-orientation: mixed;
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.banner-text {
  font-size: 18px;
  font-weight: bold;
  letter-spacing: 2px;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.start-menu-items {
  padding: 2px 0;
}

.start-menu-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 8px;
  cursor: pointer;
  user-select: none;
  position: relative;
}

.start-menu-item:hover {
  background: #316AC5;
  color: white;
}

.menu-item-icon {
  font-size: 12px;
  width: 16px;
  text-align: center;
  flex-shrink: 0;
}

.menu-item-label {
  flex: 1;
}

.menu-item-arrow {
  font-size: 8px;
  color: #808080;
}

.start-menu-item:hover .menu-item-arrow {
  color: white;
}

.start-menu-separator {
  height: 1px;
  background: #808080;
  border-bottom: 1px solid #ffffff;
  margin: 2px 8px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .taskbar {
    height: 32px;
  }
  
  .start-button {
    height: 26px;
    padding: 0 10px;
    font-size: 12px;
  }
  
  .taskbar-window {
    height: 26px;
    min-width: 100px;
    max-width: 140px;
    font-size: 10px;
  }
  
  .taskbar-clock {
    font-size: 10px;
    padding: 3px 8px;
  }
  
  .start-menu {
    bottom: 32px;
    width: 180px;
  }
}

/* Scrollbar for taskbar windows on small screens */
.taskbar-windows::-webkit-scrollbar {
  height: 4px;
}

.taskbar-windows::-webkit-scrollbar-track {
  background: #c0c0c0;
}

.taskbar-windows::-webkit-scrollbar-thumb {
  background: #808080;
  border-radius: 2px;
}

.taskbar-windows::-webkit-scrollbar-thumb:hover {
  background: #606060;
}
