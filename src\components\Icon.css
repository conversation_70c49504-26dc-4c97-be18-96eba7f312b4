.desktop-icon {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 64px;
  padding: 4px;
  cursor: pointer;
  border: 1px solid transparent;
  background: transparent;
  border-radius: 0;
  outline: none;
  user-select: none;
  transition: all 0.1s ease;
}

.desktop-icon:hover {
  background: rgba(255, 255, 255, 0.1);
}

.desktop-icon.selected {
  background: #316AC5;
  border: 1px dotted #ffffff;
}

.desktop-icon.selected .icon-text {
  color: #ffffff;
  background: #316AC5;
}

.icon-image {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 2px;
  background: #c0c0c0;
  border: 1px outset #c0c0c0;
  box-shadow: 
    inset 1px 1px 0 #ffffff,
    inset -1px -1px 0 #808080;
  position: relative;
}

.icon-emoji {
  font-size: 20px;
  line-height: 1;
  filter: drop-shadow(1px 1px 1px rgba(0, 0, 0, 0.3));
}

.icon-label {
  width: 100%;
  text-align: center;
  margin-top: 2px;
}

.icon-text {
  font-family: 'MS Sans Serif', sans-serif;
  font-size: 11px;
  color: #ffffff;
  text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.8);
  background: transparent;
  padding: 1px 2px;
  border-radius: 0;
  line-height: 1.2;
  word-wrap: break-word;
  display: inline-block;
  max-width: 100%;
}

/* Windows 95 style focus outline */
.desktop-icon:focus {
  outline: 1px dotted #ffffff;
  outline-offset: 1px;
}

/* Active state for mouse down */
.desktop-icon:active .icon-image {
  border: 1px inset #c0c0c0;
  box-shadow: 
    inset -1px -1px 0 #ffffff,
    inset 1px 1px 0 #808080;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .desktop-icon {
    width: 56px;
  }
  
  .icon-image {
    width: 28px;
    height: 28px;
  }
  
  .icon-emoji {
    font-size: 18px;
  }
  
  .icon-text {
    font-size: 10px;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .desktop-icon {
    border: 1px solid #000000;
  }
  
  .desktop-icon.selected {
    background: #0000ff;
    border: 2px solid #ffffff;
  }
  
  .icon-text {
    text-shadow: 2px 2px 2px rgba(0, 0, 0, 1);
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .desktop-icon {
    transition: none;
  }
}
