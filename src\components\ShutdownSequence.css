.shutdown-sequence {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: #008080;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 99999;
  font-family: 'MS Sans Serif', sans-serif;
}

.shutdown-sequence-content {
  text-align: center;
  color: white;
  max-width: 500px;
  padding: 40px;
}

.shutdown-logo {
  margin-bottom: 60px;
}

.logo-text {
  font-size: 48px;
  font-weight: bold;
  margin-bottom: 8px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  letter-spacing: 2px;
}

.logo-subtitle {
  font-size: 16px;
  opacity: 0.9;
  letter-spacing: 1px;
}

.shutdown-progress-section {
  margin-bottom: 40px;
}

.shutdown-message {
  font-size: 18px;
  margin-bottom: 20px;
  font-weight: bold;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.shutdown-progress-bar {
  width: 300px;
  height: 20px;
  background: #004040;
  border: 2px solid #ffffff;
  margin: 0 auto 10px;
  position: relative;
  overflow: hidden;
}

.shutdown-progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #00ffff 0%, #ffffff 50%, #00ffff 100%);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

.shutdown-progress-text {
  font-size: 14px;
  font-weight: bold;
}

.shutdown-dots {
  display: flex;
  justify-content: center;
  gap: 8px;
  font-size: 24px;
}

.shutdown-final {
  text-align: center;
}

.shutdown-final-icon {
  font-size: 64px;
  margin-bottom: 20px;
}

.shutdown-final-message {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 16px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.shutdown-final-submessage {
  font-size: 16px;
  opacity: 0.9;
  margin-bottom: 30px;
}

.shutdown-restart-btn {
  background: #c0c0c0;
  border: 2px outset #c0c0c0;
  color: #000080;
  font-family: 'MS Sans Serif', sans-serif;
  font-size: 14px;
  font-weight: bold;
  padding: 12px 24px;
  cursor: pointer;
  border-radius: 4px;
  box-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.shutdown-restart-btn:hover {
  background: #d0d0d0;
}

.shutdown-restart-btn:active {
  border: 2px inset #c0c0c0;
  background: #a0a0a0;
}

/* Scanlines effect for authenticity */
.shutdown-sequence::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    transparent 50%, 
    rgba(0, 0, 0, 0.03) 50%
  );
  background-size: 100% 4px;
  pointer-events: none;
  z-index: 1;
}

.shutdown-sequence-content {
  position: relative;
  z-index: 2;
}

/* CRT flicker effect */
@keyframes flicker {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.98; }
}

.shutdown-sequence {
  animation: flicker 0.15s infinite alternate;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .shutdown-sequence-content {
    padding: 20px;
  }
  
  .logo-text {
    font-size: 36px;
  }
  
  .logo-subtitle {
    font-size: 14px;
  }
  
  .shutdown-message {
    font-size: 16px;
  }
  
  .shutdown-progress-bar {
    width: 250px;
    height: 16px;
  }
  
  .shutdown-final-message {
    font-size: 20px;
  }
  
  .shutdown-final-submessage {
    font-size: 14px;
  }
  
  .shutdown-restart-btn {
    font-size: 12px;
    padding: 10px 20px;
  }
}

@media (max-width: 480px) {
  .logo-text {
    font-size: 28px;
  }
  
  .shutdown-message {
    font-size: 14px;
  }
  
  .shutdown-progress-bar {
    width: 200px;
    height: 14px;
  }
  
  .shutdown-final-icon {
    font-size: 48px;
  }
  
  .shutdown-final-message {
    font-size: 18px;
  }
}
