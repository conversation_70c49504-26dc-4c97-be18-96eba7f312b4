import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import './ShutdownSequence.css';

interface ShutdownSequenceProps {
  onComplete: () => void;
}

const ShutdownSequence: React.FC<ShutdownSequenceProps> = ({ onComplete }) => {
  const [currentStage, setCurrentStage] = useState('saving');
  const [progress, setProgress] = useState(0);

  const stages = [
    { id: 'saving', text: 'Saving your settings...', duration: 2000 },
    { id: 'closing', text: 'Closing applications...', duration: 1500 },
    { id: 'shutdown', text: 'Windows is shutting down...', duration: 2000 },
    { id: 'complete', text: 'It is now safe to turn off your computer.', duration: 3000 }
  ];

  useEffect(() => {
    let progressInterval: NodeJS.Timeout;
    let stageTimeout: NodeJS.Timeout;

    const currentStageIndex = stages.findIndex(stage => stage.id === currentStage);
    const currentStageData = stages[currentStageIndex];

    if (currentStageData) {
      setProgress(0);
      
      // Progress animation
      progressInterval = setInterval(() => {
        setProgress(prev => {
          if (prev >= 100) {
            clearInterval(progressInterval);
            return 100;
          }
          return prev + (100 / (currentStageData.duration / 50));
        });
      }, 50);

      // Move to next stage
      stageTimeout = setTimeout(() => {
        if (currentStageIndex < stages.length - 1) {
          setCurrentStage(stages[currentStageIndex + 1].id);
        } else {
          // Final stage - show completion message
          setTimeout(onComplete, 1000);
        }
      }, currentStageData.duration);
    }

    return () => {
      clearInterval(progressInterval);
      clearTimeout(stageTimeout);
    };
  }, [currentStage, onComplete]);

  const getCurrentStageText = () => {
    const stage = stages.find(s => s.id === currentStage);
    return stage ? stage.text : '';
  };

  return (
    <motion.div
      className="shutdown-sequence"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      transition={{ duration: 0.5 }}
    >
      <div className="shutdown-sequence-content">
        {currentStage !== 'complete' ? (
          <>
            {/* Windows 95 Logo */}
            <div className="shutdown-logo">
              <div className="logo-text">Windows 95</div>
              <div className="logo-subtitle">Microsoft</div>
            </div>

            {/* Progress Section */}
            <div className="shutdown-progress-section">
              <div className="shutdown-message">
                {getCurrentStageText()}
              </div>
              
              <div className="shutdown-progress-bar">
                <motion.div
                  className="shutdown-progress-fill"
                  initial={{ width: 0 }}
                  animate={{ width: `${progress}%` }}
                  transition={{ duration: 0.1 }}
                />
              </div>
              
              <div className="shutdown-progress-text">
                {Math.round(progress)}%
              </div>
            </div>

            {/* Animated dots */}
            <div className="shutdown-dots">
              <motion.span
                animate={{ opacity: [0, 1, 0] }}
                transition={{ duration: 0.6, repeat: Infinity, delay: 0 }}
              >
                •
              </motion.span>
              <motion.span
                animate={{ opacity: [0, 1, 0] }}
                transition={{ duration: 0.6, repeat: Infinity, delay: 0.2 }}
              >
                •
              </motion.span>
              <motion.span
                animate={{ opacity: [0, 1, 0] }}
                transition={{ duration: 0.6, repeat: Infinity, delay: 0.4 }}
              >
                •
              </motion.span>
            </div>
          </>
        ) : (
          /* Final shutdown message */
          <motion.div
            className="shutdown-final"
            initial={{ scale: 0.8, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ duration: 0.5 }}
          >
            <div className="shutdown-final-icon">💻</div>
            <div className="shutdown-final-message">
              It is now safe to turn off your computer.
            </div>
            <div className="shutdown-final-submessage">
              Thank you for visiting my Windows 95 portfolio!
            </div>
            <motion.button
              className="shutdown-restart-btn"
              onClick={onComplete}
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 2 }}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              🔄 Restart Portfolio
            </motion.button>
          </motion.div>
        )}
      </div>
    </motion.div>
  );
};

export default ShutdownSequence;
