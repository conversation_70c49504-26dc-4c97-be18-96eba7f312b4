# Windows 95 Portfolio Website 🪟

A nostalgic developer portfolio website that recreates the classic Windows 95 desktop experience using modern React and TypeScript.

![Windows 95 Portfolio](https://img.shields.io/badge/Windows-95-blue?style=for-the-badge&logo=windows95)
![React](https://img.shields.io/badge/React-18-blue?style=for-the-badge&logo=react)
![TypeScript](https://img.shields.io/badge/TypeScript-5-blue?style=for-the-badge&logo=typescript)
![Framer Motion](https://img.shields.io/badge/Framer%20Motion-11-purple?style=for-the-badge)

## ✨ Features

### 🎮 Authentic Windows 95 Experience
- **Boot Sequence**: Complete with CRT flicker, BIOS screen, and Windows 95 startup animation
- **Desktop Interface**: Classic wallpaper, desktop icons, and authentic styling
- **Window Management**: Draggable, resizable windows with minimize/close functionality
- **Taskbar**: Start button, active window management, and system clock
- **Sound Effects**: Retro beeps and clicks for authentic interaction feedback

### 🖱️ Interactive Elements
- **Desktop Icons**: Double-click to open applications (About Me, Projects, Contact, etc.)
- **Right-Click Context Menu**: Desktop properties and refresh options
- **Draggable Windows**: Move windows around the desktop
- **Start Menu**: Access to applications and easter eggs

### 🎯 Portfolio Content
- **About Me**: Personal introduction and skills showcase
- **Projects**: Portfolio of development work
- **Contact**: Professional contact information
- **My Computer**: System information display
- **Notepad**: Functional text editor with Windows 95 styling
- **Calculator**: Classic calculator interface

### 🎪 Easter Eggs
- **Blue Screen of Death**: Nostalgic BSOD with recovery animation
- **Authentic Sounds**: Web Audio API generated retro sound effects
- **Hidden Features**: Right-click menus and system dialogs

## 🛠️ Technical Stack

- **Frontend**: React 18 with TypeScript
- **Animations**: Framer Motion for smooth transitions
- **Interactions**: react-draggable for window management
- **Styling**: Custom CSS with authentic Windows 95 design system
- **Build Tool**: Vite for fast development and building
- **Sound**: Web Audio API for retro sound effects

## 🚀 Getting Started

### Prerequisites
- Node.js 16+
- npm or yarn

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd landing
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Start development server**
   ```bash
   npm run dev
   ```

4. **Open your browser**
   Navigate to `http://localhost:5173`

### Building for Production

```bash
npm run build
```

## 🎨 Design Philosophy

This project recreates the Windows 95 aesthetic with pixel-perfect attention to detail:

- **Color Palette**: Classic Windows 95 grays (#c0c0c0, #808080)
- **Typography**: MS Sans Serif and Courier New fonts
- **UI Elements**: Authentic button styles, window chrome, and system dialogs
- **Interactions**: Period-appropriate animations and sound effects

## 📁 Project Structure

```
src/
├── components/
│   ├── BootScreen.tsx      # Startup sequence animation
│   ├── Desktop.tsx         # Main desktop interface
│   ├── Icon.tsx           # Desktop icon component
│   ├── Window.tsx         # Draggable window container
│   ├── Taskbar.tsx        # Bottom taskbar with Start menu
│   ├── ContextMenu.tsx    # Right-click context menu
│   ├── Notepad.tsx        # Text editor application
│   ├── BSOD.tsx          # Blue Screen of Death easter egg
│   └── *.css             # Component-specific styles
├── utils/
│   └── sounds.ts         # Web Audio API sound manager
├── App.tsx               # Main application component
├── index.css             # Global Windows 95 styles
└── main.tsx             # Application entry point
```

## 🎵 Sound System

The application includes a custom sound manager that generates authentic Windows 95-style sounds using the Web Audio API:

- Startup chimes
- Click sounds
- Window minimize/maximize/close sounds
- Error beeps

## 🌟 Key Components

### BootScreen
Simulates the classic Windows 95 boot sequence with:
- CRT monitor flicker effect
- BIOS text animation
- Windows 95 logo with loading bar

### Desktop
Main interface featuring:
- Authentic wallpaper pattern
- Icon grid layout
- Window management system
- Right-click context menu

### Window
Fully functional window system with:
- Draggable title bars
- Minimize/close buttons
- Resizable corners
- Focus management

## 🎯 Browser Compatibility

- Chrome/Edge 88+
- Firefox 85+
- Safari 14+

## 📱 Responsive Design

While maintaining the Windows 95 aesthetic, the interface adapts to different screen sizes:
- Mobile-friendly touch interactions
- Responsive icon layouts
- Scalable window sizes

## 🤝 Contributing

Feel free to contribute to this nostalgic project! Areas for enhancement:
- Additional applications (Paint, Solitaire, etc.)
- More sound effects
- Enhanced animations
- Additional easter eggs

## 📄 License

This project is open source and available under the MIT License.

## 🙏 Acknowledgments

- Microsoft for the original Windows 95 design
- The React and TypeScript communities
- Framer Motion for excellent animation capabilities
- All the developers keeping retro computing alive

---

*Built with ❤️ and nostalgia for the golden age of computing*
