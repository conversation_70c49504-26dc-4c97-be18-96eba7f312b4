.context-menu {
  position: fixed;
  background: #c0c0c0;
  border: 2px outset #c0c0c0;
  box-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  font-family: 'MS Sans Serif', sans-serif;
  font-size: 11px;
  z-index: 10000;
  min-width: 150px;
  padding: 2px 0;
  user-select: none;
}

.context-menu-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 16px 4px 8px;
  cursor: pointer;
  color: #000;
  position: relative;
}

.context-menu-item:hover:not(.disabled) {
  background: #316AC5;
  color: white;
}

.context-menu-item.disabled {
  color: #808080;
  cursor: default;
}

.context-menu-icon {
  font-size: 12px;
  width: 16px;
  text-align: center;
  flex-shrink: 0;
}

.context-menu-label {
  flex: 1;
  white-space: nowrap;
}

.context-menu-separator {
  height: 1px;
  background: #808080;
  border-bottom: 1px solid #ffffff;
  margin: 2px 8px;
}

/* Submenu arrow (for future enhancement) */
.context-menu-item.has-submenu::after {
  content: '▶';
  position: absolute;
  right: 8px;
  font-size: 8px;
  color: #808080;
}

.context-menu-item.has-submenu:hover::after {
  color: white;
}

/* Keyboard navigation support */
.context-menu-item:focus {
  background: #316AC5;
  color: white;
  outline: none;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .context-menu {
    border: 3px solid #000000;
    background: #ffffff;
  }
  
  .context-menu-item {
    color: #000000;
  }
  
  .context-menu-item:hover:not(.disabled) {
    background: #000000;
    color: #ffffff;
  }
  
  .context-menu-separator {
    background: #000000;
    border-bottom: none;
  }
}
