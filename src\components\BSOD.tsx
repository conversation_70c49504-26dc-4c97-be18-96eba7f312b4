import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import './BSOD.css';

interface BSODProps {
  onClose: () => void;
}

const BSOD: React.FC<BSODProps> = ({ onClose }) => {
  const [progress, setProgress] = useState(0);

  useEffect(() => {
    const timer = setInterval(() => {
      setProgress(prev => {
        if (prev >= 100) {
          clearInterval(timer);
          setTimeout(onClose, 1000);
          return 100;
        }
        return prev + 1;
      });
    }, 50);

    return () => clearInterval(timer);
  }, [onClose]);

  const handleKeyPress = (event: React.KeyboardEvent) => {
    if (event.key === 'Escape' || event.key === 'Enter') {
      onClose();
    }
  };

  return (
    <motion.div
      className="bsod"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      transition={{ duration: 0.3 }}
      onKeyDown={handleKeyPress}
      tabIndex={0}
    >
      <div className="bsod-content">
        <div className="bsod-header">
          <h1>Windows</h1>
        </div>
        
        <div className="bsod-message">
          <p>A fatal exception 0E has occurred at 0028:C0011E36 in VXD VMM(01) +</p>
          <p>00010E36. The current application will be terminated.</p>
          <br />
          <p>* Press any key to terminate the current application.</p>
          <p>* Press CTRL+ALT+DEL to restart your computer. You will</p>
          <p>  lose any unsaved information in all open applications.</p>
          <br />
          <p>Press any key to continue _</p>
        </div>

        <div className="bsod-footer">
          <div className="progress-container">
            <div className="progress-label">System Recovery: {progress}%</div>
            <div className="progress-bar">
              <motion.div
                className="progress-fill"
                initial={{ width: 0 }}
                animate={{ width: `${progress}%` }}
                transition={{ duration: 0.1 }}
              />
            </div>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

export default BSOD;
