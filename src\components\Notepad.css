.notepad-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: white;
}

.notepad-textarea {
  flex: 1;
  border: none;
  outline: none;
  padding: 8px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.4;
  background: white;
  color: black;
  resize: none;
  overflow-y: auto;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.notepad-textarea::-webkit-scrollbar {
  width: 16px;
}

.notepad-textarea::-webkit-scrollbar-track {
  background: #c0c0c0;
  border: 1px inset #c0c0c0;
}

.notepad-textarea::-webkit-scrollbar-thumb {
  background: #c0c0c0;
  border: 1px outset #c0c0c0;
}

.notepad-textarea::-webkit-scrollbar-thumb:hover {
  background: #a0a0a0;
}

.notepad-textarea::-webkit-scrollbar-button {
  background: #c0c0c0;
  border: 1px outset #c0c0c0;
  height: 16px;
}

.notepad-textarea::-webkit-scrollbar-button:hover {
  background: #a0a0a0;
}

.notepad-status {
  height: 20px;
  background: #c0c0c0;
  border-top: 1px solid #808080;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 8px;
  font-family: 'MS Sans Serif', sans-serif;
  font-size: 10px;
  color: #000;
}

/* Selection styling */
.notepad-textarea::selection {
  background: #316AC5;
  color: white;
}

.notepad-textarea::-moz-selection {
  background: #316AC5;
  color: white;
}
