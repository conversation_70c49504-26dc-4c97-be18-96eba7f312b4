import React, { useState, useCallback } from 'react';
import { motion } from 'framer-motion';
import { soundManager } from '../utils/sounds';
import './Icon.css';

interface IconProps {
  title: string;
  icon: string;
  onDoubleClick: () => void;
  onSingleClick?: () => void;
}

const Icon: React.FC<IconProps> = ({ title, icon, onDoubleClick, onSingleClick }) => {
  const [isSelected, setIsSelected] = useState(false);
  const [clickCount, setClickCount] = useState(0);

  const handleClick = useCallback(() => {
    setIsSelected(true);
    soundManager.playClickSound();

    if (onSingleClick) {
      onSingleClick();
    }

    setClickCount(prev => {
      const newCount = prev + 1;

      // Handle double-click
      if (newCount === 2) {
        onDoubleClick();
        return 0; // Reset count
      }

      // Reset count after 300ms if no second click
      setTimeout(() => {
        setClickCount(0);
      }, 300);

      return newCount;
    });
  }, [onDoubleClick, onSingleClick]);

  const handleMouseDown = useCallback(() => {
    setIsSelected(true);
  }, []);

  const handleBlur = useCallback(() => {
    setIsSelected(false);
  }, []);

  return (
    <motion.div
      className={`desktop-icon ${isSelected ? 'selected' : ''}`}
      onClick={handleClick}
      onMouseDown={handleMouseDown}
      onBlur={handleBlur}
      tabIndex={0}
      whileHover={{ scale: 1.05 }}
      whileTap={{ scale: 0.95 }}
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.2 }}
    >
      <div className="icon-image">
        <span className="icon-emoji">{icon}</span>
      </div>
      <div className="icon-label">
        <span className="icon-text">{title}</span>
      </div>
    </motion.div>
  );
};

export default Icon;
