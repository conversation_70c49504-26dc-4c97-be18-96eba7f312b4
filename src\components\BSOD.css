.bsod {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: #0000aa;
  color: #ffffff;
  font-family: 'Courier New', monospace;
  font-size: 14px;
  z-index: 99999;
  display: flex;
  align-items: center;
  justify-content: center;
  outline: none;
  cursor: default;
  user-select: none;
}

.bsod-content {
  width: 80%;
  max-width: 600px;
  text-align: left;
}

.bsod-header {
  text-align: center;
  margin-bottom: 40px;
}

.bsod-header h1 {
  font-size: 24px;
  font-weight: normal;
  margin: 0;
  letter-spacing: 2px;
}

.bsod-message {
  line-height: 1.4;
  margin-bottom: 60px;
}

.bsod-message p {
  margin: 0 0 4px 0;
}

.bsod-footer {
  position: fixed;
  bottom: 40px;
  left: 50%;
  transform: translateX(-50%);
  width: 300px;
}

.progress-container {
  text-align: center;
}

.progress-label {
  margin-bottom: 10px;
  font-size: 12px;
}

.progress-bar {
  width: 100%;
  height: 20px;
  background: #000080;
  border: 2px solid #ffffff;
  position: relative;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: #ffffff;
  transition: width 0.1s ease;
}

/* Blinking cursor effect */
.bsod-message p:last-child::after {
  content: '';
  display: inline-block;
  width: 8px;
  height: 14px;
  background: #ffffff;
  margin-left: 2px;
  animation: blink 1s infinite;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

/* Scanlines effect for authenticity */
.bsod::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    transparent 50%, 
    rgba(255, 255, 255, 0.02) 50%
  );
  background-size: 100% 4px;
  pointer-events: none;
  z-index: 1;
}

.bsod-content {
  position: relative;
  z-index: 2;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .bsod {
    font-size: 12px;
  }
  
  .bsod-header h1 {
    font-size: 20px;
  }
  
  .bsod-content {
    width: 90%;
    padding: 20px;
  }
  
  .bsod-footer {
    width: 250px;
    bottom: 20px;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .bsod {
    background: #000000;
    color: #ffffff;
  }
  
  .progress-bar {
    border: 3px solid #ffffff;
  }
}
