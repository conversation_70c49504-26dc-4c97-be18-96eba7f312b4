/* Windows 95 Global Styles */
* {
  box-sizing: border-box;
}

:root {
  --win95-gray: #c0c0c0;
  --win95-dark-gray: #808080;
  --win95-light-gray: #e0e0e0;
  --win95-blue: #0000ff;
  --win95-dark-blue: #000080;
  --win95-white: #ffffff;
  --win95-black: #000000;
  --win95-green: #00ff00;
  --win95-red: #ff0000;
  --win95-yellow: #ffff00;
}

body {
  margin: 0;
  padding: 0;
  font-family: 'MS Sans Serif', sans-serif;
  font-size: 11px;
  background: var(--win95-gray);
  color: var(--win95-black);
  overflow: hidden;
  user-select: none;
  -webkit-font-smoothing: none;
  -moz-osx-font-smoothing: unset;
}

#root {
  width: 100vw;
  height: 100vh;
  margin: 0;
  padding: 0;
}

/* Windows 95 Button Styles */
button {
  font-family: 'MS Sans Serif', sans-serif;
  font-size: 11px;
  background: var(--win95-gray);
  border: 1px outset var(--win95-gray);
  color: var(--win95-black);
  cursor: pointer;
  padding: 2px 6px;
  border-radius: 0;
  outline: none;
}

button:hover {
  background: var(--win95-light-gray);
}

button:active {
  border: 1px inset var(--win95-gray);
  background: var(--win95-dark-gray);
}

button:focus {
  outline: 1px dotted var(--win95-black);
  outline-offset: -3px;
}

/* Remove modern styling */
a {
  color: var(--win95-blue);
  text-decoration: underline;
}

a:hover {
  color: var(--win95-dark-blue);
}

/* Scrollbar styling for Windows 95 look */
::-webkit-scrollbar {
  width: 16px;
  height: 16px;
}

::-webkit-scrollbar-track {
  background: var(--win95-gray);
  border: 1px inset var(--win95-gray);
}

::-webkit-scrollbar-thumb {
  background: var(--win95-gray);
  border: 1px outset var(--win95-gray);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--win95-light-gray);
}

::-webkit-scrollbar-button {
  background: var(--win95-gray);
  border: 1px outset var(--win95-gray);
  height: 16px;
  width: 16px;
}

::-webkit-scrollbar-button:hover {
  background: var(--win95-light-gray);
}

/* Disable text selection for UI elements */
.no-select {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}
