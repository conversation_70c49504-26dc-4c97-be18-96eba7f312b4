.desktop {
  width: 100vw;
  height: 100vh;
  position: relative;
  overflow: hidden;
  font-family: 'MS Sans Serif', sans-serif;
  user-select: none;
}

.desktop-wallpaper {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, #008080 0%, #004040 50%, #008080 100%);
  background-size: 100px 100px;
  background-image: 
    radial-gradient(circle at 25px 25px, rgba(255,255,255,0.1) 2px, transparent 2px),
    radial-gradient(circle at 75px 75px, rgba(255,255,255,0.1) 2px, transparent 2px);
  z-index: 0;
}

.desktop-icons {
  position: absolute;
  top: 20px;
  left: 20px;
  display: flex;
  flex-direction: column;
  gap: 20px;
  z-index: 1;
}

/* Window Content Styles */
.window-content {
  padding: 16px;
  font-family: 'MS Sans Serif', sans-serif;
  font-size: 11px;
  line-height: 1.4;
  color: #000;
  height: 100%;
  overflow-y: auto;
  box-sizing: border-box;
}

.window-content h2 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: bold;
  color: #000080;
}

.window-content h3 {
  margin: 8px 0 4px 0;
  font-size: 12px;
  font-weight: bold;
}

.window-content p {
  margin: 0 0 8px 0;
}

.window-content ul {
  margin: 0 0 8px 16px;
  padding: 0;
}

.window-content li {
  margin: 2px 0;
}

/* Project List Styles */
.project-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.project-item {
  padding: 8px;
  border: 1px solid #808080;
  background: #f0f0f0;
  border-style: inset;
}

.project-item h3 {
  margin: 0 0 4px 0;
  color: #000080;
}

.project-item p {
  margin: 0;
  font-size: 10px;
}

/* Contact Info Styles */
.contact-info p {
  margin: 4px 0;
  font-family: 'Courier New', monospace;
  font-size: 10px;
}

/* Computer Info Styles */
.computer-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 16px;
}

.drive-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 8px;
  border: 1px solid transparent;
  cursor: pointer;
  font-size: 11px;
}

.drive-item:hover {
  background: #316AC5;
  color: white;
  border: 1px solid #0000ff;
}

.drive-icon {
  font-size: 16px;
  width: 20px;
  text-align: center;
}

/* Scrollbar Styles for Windows 95 look */
.window-content::-webkit-scrollbar {
  width: 16px;
}

.window-content::-webkit-scrollbar-track {
  background: #c0c0c0;
  border: 1px inset #c0c0c0;
}

.window-content::-webkit-scrollbar-thumb {
  background: #c0c0c0;
  border: 1px outset #c0c0c0;
}

.window-content::-webkit-scrollbar-thumb:hover {
  background: #a0a0a0;
}

.window-content::-webkit-scrollbar-button {
  background: #c0c0c0;
  border: 1px outset #c0c0c0;
  height: 16px;
}

.window-content::-webkit-scrollbar-button:hover {
  background: #a0a0a0;
}

.window-content::-webkit-scrollbar-button:vertical:start:decrement {
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16'%3E%3Cpath d='M8 4l4 4H4z' fill='%23000'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: center;
}

.window-content::-webkit-scrollbar-button:vertical:end:increment {
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16'%3E%3Cpath d='M4 8l4 4 4-4z' fill='%23000'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: center;
}

/* Calculator Styles */
.calculator-display {
  margin-bottom: 8px;
}

.calc-screen {
  background: #000;
  color: #00ff00;
  font-family: 'Courier New', monospace;
  font-size: 16px;
  padding: 8px;
  text-align: right;
  border: 1px inset #c0c0c0;
  min-height: 24px;
}

.calculator-buttons {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.calc-row {
  display: flex;
  gap: 2px;
}

.calc-btn {
  flex: 1;
  height: 32px;
  background: #c0c0c0;
  border: 1px outset #c0c0c0;
  font-family: 'MS Sans Serif', sans-serif;
  font-size: 11px;
  cursor: pointer;
  user-select: none;
}

.calc-btn:hover {
  background: #d0d0d0;
}

.calc-btn:active {
  border: 1px inset #c0c0c0;
  background: #a0a0a0;
}

.calc-zero {
  flex: 2;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .desktop-icons {
    flex-direction: row;
    flex-wrap: wrap;
    top: 10px;
    left: 10px;
    gap: 10px;
  }

  .window-content {
    font-size: 12px;
    padding: 12px;
  }

  .project-item {
    padding: 6px;
  }

  .calc-btn {
    height: 28px;
    font-size: 10px;
  }
}
