import React, { useRef, useCallback, useState } from 'react';
import Draggable, { DraggableData, DraggableEvent } from 'react-draggable';
import { motion } from 'framer-motion';
import { soundManager } from '../utils/sounds';
import './Window.css';

interface WindowProps {
  id: string;
  title: string;
  children: React.ReactNode;
  position: { x: number; y: number };
  size: { width: number; height: number };
  zIndex: number;
  onClose: () => void;
  onMinimize: () => void;
  onFocus: () => void;
  onPositionChange: (position: { x: number; y: number }) => void;
  onSizeChange: (size: { width: number; height: number }) => void;
}

const Window: React.FC<WindowProps> = ({
  id,
  title,
  children,
  position,
  size,
  zIndex,
  onClose,
  onMinimize,
  onFocus,
  onPositionChange,
  onSizeChange
}) => {
  const windowRef = useRef<HTMLDivElement>(null);
  const [isResizing, setIsResizing] = useState(false);
  const [resizeStart, setResizeStart] = useState({ x: 0, y: 0, width: 0, height: 0 });

  const handleDrag = useCallback((e: DraggableEvent, data: DraggableData) => {
    onPositionChange({ x: data.x, y: data.y });
  }, [onPositionChange]);

  const handleMouseDown = useCallback(() => {
    onFocus();
  }, [onFocus]);

  const handleResizeStart = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsResizing(true);
    setResizeStart({
      x: e.clientX,
      y: e.clientY,
      width: size.width,
      height: size.height
    });

    const handleMouseMove = (moveEvent: MouseEvent) => {
      const deltaX = moveEvent.clientX - resizeStart.x;
      const deltaY = moveEvent.clientY - resizeStart.y;
      
      const newWidth = Math.max(200, resizeStart.width + deltaX);
      const newHeight = Math.max(150, resizeStart.height + deltaY);
      
      onSizeChange({ width: newWidth, height: newHeight });
    };

    const handleMouseUp = () => {
      setIsResizing(false);
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  }, [size, resizeStart, onSizeChange]);

  return (
    <Draggable
      handle=".window-title-bar"
      position={position}
      onDrag={handleDrag}
      bounds="parent"
    >
      <motion.div
        ref={windowRef}
        className="window"
        style={{
          width: size.width,
          height: size.height,
          zIndex: zIndex
        }}
        onMouseDown={handleMouseDown}
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.9 }}
        transition={{ duration: 0.2 }}
      >
        <div className="window-title-bar">
          <div className="window-title">
            <span className="window-icon">📄</span>
            <span className="window-title-text">{title}</span>
          </div>
          <div className="window-controls">
            <button
              className="window-button minimize-button"
              onClick={() => {
                soundManager.playMinimizeSound();
                onMinimize();
              }}
              title="Minimize"
            >
              <span className="button-icon">_</span>
            </button>
            <button
              className="window-button close-button"
              onClick={() => {
                soundManager.playCloseSound();
                onClose();
              }}
              title="Close"
            >
              <span className="button-icon">×</span>
            </button>
          </div>
        </div>
        
        <div className="window-menu-bar">
          <div className="menu-item">File</div>
          <div className="menu-item">Edit</div>
          <div className="menu-item">View</div>
          <div className="menu-item">Help</div>
        </div>
        
        <div className="window-content-area">
          {children}
        </div>
        
        <div className="window-status-bar">
          <div className="status-text">Ready</div>
          <div className="status-resize-grip" 
               onMouseDown={handleResizeStart}
               title="Resize window"
          />
        </div>
      </motion.div>
    </Draggable>
  );
};

export default Window;
