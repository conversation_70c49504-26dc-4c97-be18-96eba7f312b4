.shutdown-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
}

.shutdown-dialog {
  width: 400px;
  background: #c0c0c0;
  border: 2px outset #c0c0c0;
  box-shadow: 4px 4px 8px rgba(0, 0, 0, 0.3);
  font-family: 'MS Sans Serif', sans-serif;
  font-size: 11px;
  outline: none;
}

.shutdown-title-bar {
  background: linear-gradient(90deg, #0000ff 0%, #000080 100%);
  color: white;
  padding: 2px;
  display: flex;
  align-items: center;
  height: 18px;
}

.shutdown-title {
  display: flex;
  align-items: center;
  gap: 4px;
  padding-left: 4px;
  font-weight: bold;
  font-size: 11px;
}

.shutdown-icon {
  font-size: 12px;
}

.shutdown-content {
  display: flex;
  padding: 16px;
  gap: 16px;
  align-items: flex-start;
}

.shutdown-icon-large {
  font-size: 32px;
  flex-shrink: 0;
}

.shutdown-text {
  flex: 1;
}

.shutdown-text h3 {
  margin: 0 0 16px 0;
  font-size: 11px;
  font-weight: bold;
  color: #000080;
}

.shutdown-options {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.shutdown-option {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  cursor: pointer;
  padding: 4px;
  border-radius: 2px;
}

.shutdown-option:hover {
  background: rgba(0, 0, 255, 0.1);
}

.shutdown-option input[type="radio"] {
  display: none;
}

.radio-custom {
  width: 12px;
  height: 12px;
  border: 1px solid #808080;
  border-radius: 50%;
  background: white;
  position: relative;
  flex-shrink: 0;
  margin-top: 2px;
}

.shutdown-option input[type="radio"]:checked + .radio-custom::after {
  content: '';
  position: absolute;
  top: 2px;
  left: 2px;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: #000080;
}

.option-text {
  flex: 1;
  line-height: 1.3;
}

.option-text strong {
  color: #000080;
}

.option-text small {
  color: #666;
  font-size: 10px;
}

.shutdown-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  padding: 12px 16px;
  border-top: 1px solid #808080;
  background: #c0c0c0;
}

.shutdown-btn {
  width: 75px;
  height: 23px;
  background: #c0c0c0;
  border: 1px outset #c0c0c0;
  font-family: 'MS Sans Serif', sans-serif;
  font-size: 11px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.shutdown-btn:hover {
  background: #d0d0d0;
}

.shutdown-btn:active {
  border: 1px inset #c0c0c0;
  background: #a0a0a0;
}

.shutdown-btn-ok {
  font-weight: bold;
}

/* Keyboard navigation */
.shutdown-dialog:focus-within .shutdown-btn-ok {
  border: 2px solid #000080;
}

/* Animation for radio button selection */
.radio-custom {
  transition: all 0.1s ease;
}

.shutdown-option:hover .radio-custom {
  border-color: #000080;
}

/* Responsive adjustments */
@media (max-width: 480px) {
  .shutdown-dialog {
    width: 90%;
    max-width: 350px;
  }
  
  .shutdown-content {
    padding: 12px;
    gap: 12px;
  }
  
  .shutdown-icon-large {
    font-size: 24px;
  }
  
  .shutdown-buttons {
    padding: 8px 12px;
  }
  
  .shutdown-btn {
    width: 65px;
    height: 20px;
    font-size: 10px;
  }
}
