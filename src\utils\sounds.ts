// Simple sound utility for Windows 95 effects
export class SoundManager {
  private static instance: SoundManager;
  private audioContext: AudioContext | null = null;

  private constructor() {
    try {
      this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
    } catch (e) {
      console.warn('Web Audio API not supported');
    }
  }

  static getInstance(): SoundManager {
    if (!SoundManager.instance) {
      SoundManager.instance = new SoundManager();
    }
    return SoundManager.instance;
  }

  private createBeep(frequency: number, duration: number, volume: number = 0.1): void {
    if (!this.audioContext) return;

    const oscillator = this.audioContext.createOscillator();
    const gainNode = this.audioContext.createGain();

    oscillator.connect(gainNode);
    gainNode.connect(this.audioContext.destination);

    oscillator.frequency.value = frequency;
    oscillator.type = 'square';

    gainNode.gain.setValueAtTime(0, this.audioContext.currentTime);
    gainNode.gain.linearRampToValueAtTime(volume, this.audioContext.currentTime + 0.01);
    gainNode.gain.exponentialRampToValueAtTime(0.001, this.audioContext.currentTime + duration);

    oscillator.start(this.audioContext.currentTime);
    oscillator.stop(this.audioContext.currentTime + duration);
  }

  playStartupSound(): void {
    // Windows 95 startup sound approximation
    setTimeout(() => this.createBeep(523, 0.2), 0);    // C5
    setTimeout(() => this.createBeep(659, 0.2), 200);  // E5
    setTimeout(() => this.createBeep(784, 0.2), 400);  // G5
    setTimeout(() => this.createBeep(1047, 0.4), 600); // C6
  }

  playClickSound(): void {
    this.createBeep(800, 0.05, 0.05);
  }

  playErrorSound(): void {
    this.createBeep(200, 0.3, 0.1);
  }

  playMinimizeSound(): void {
    this.createBeep(400, 0.1, 0.05);
    setTimeout(() => this.createBeep(300, 0.1, 0.05), 100);
  }

  playMaximizeSound(): void {
    this.createBeep(300, 0.1, 0.05);
    setTimeout(() => this.createBeep(400, 0.1, 0.05), 100);
  }

  playCloseSound(): void {
    this.createBeep(600, 0.1, 0.05);
    setTimeout(() => this.createBeep(400, 0.1, 0.05), 100);
    setTimeout(() => this.createBeep(200, 0.1, 0.05), 200);
  }
}

export const soundManager = SoundManager.getInstance();
