import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import './ShutdownDialog.css';

interface ShutdownDialogProps {
  onShutdown: () => void;
  onCancel: () => void;
}

const ShutdownDialog: React.FC<ShutdownDialogProps> = ({ onShutdown, onCancel }) => {
  const [selectedOption, setSelectedOption] = useState('shutdown');

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      if (selectedOption === 'shutdown') {
        onShutdown();
      } else {
        onCancel();
      }
    } else if (e.key === 'Escape') {
      onCancel();
    }
  };

  return (
    <motion.div
      className="shutdown-overlay"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      transition={{ duration: 0.2 }}
    >
      <motion.div
        className="shutdown-dialog"
        initial={{ scale: 0.8, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.8, opacity: 0 }}
        transition={{ duration: 0.2 }}
        onKeyDown={handleKeyPress}
        tabIndex={0}
      >
        {/* Title Bar */}
        <div className="shutdown-title-bar">
          <div className="shutdown-title">
            <span className="shutdown-icon">🪟</span>
            <span>Shut Down Windows</span>
          </div>
        </div>

        {/* Content */}
        <div className="shutdown-content">
          <div className="shutdown-icon-large">
            💻
          </div>
          
          <div className="shutdown-text">
            <h3>What do you want the computer to do?</h3>
            
            <div className="shutdown-options">
              <label className="shutdown-option">
                <input
                  type="radio"
                  name="shutdown-option"
                  value="shutdown"
                  checked={selectedOption === 'shutdown'}
                  onChange={(e) => setSelectedOption(e.target.value)}
                />
                <span className="radio-custom"></span>
                <span className="option-text">
                  <strong>Shut down the computer?</strong>
                  <br />
                  <small>Select this option to shut down your computer so that you can safely turn off the power.</small>
                </span>
              </label>

              <label className="shutdown-option">
                <input
                  type="radio"
                  name="shutdown-option"
                  value="restart"
                  checked={selectedOption === 'restart'}
                  onChange={(e) => setSelectedOption(e.target.value)}
                />
                <span className="radio-custom"></span>
                <span className="option-text">
                  <strong>Restart the computer?</strong>
                  <br />
                  <small>Select this option to restart your computer and reload Windows 95.</small>
                </span>
              </label>

              <label className="shutdown-option">
                <input
                  type="radio"
                  name="shutdown-option"
                  value="close"
                  checked={selectedOption === 'close'}
                  onChange={(e) => setSelectedOption(e.target.value)}
                />
                <span className="radio-custom"></span>
                <span className="option-text">
                  <strong>Close all programs and log on as a different user?</strong>
                  <br />
                  <small>Select this option to close all programs so that another user can log on.</small>
                </span>
              </label>
            </div>
          </div>
        </div>

        {/* Buttons */}
        <div className="shutdown-buttons">
          <button
            className="shutdown-btn shutdown-btn-ok"
            onClick={() => {
              if (selectedOption === 'shutdown') {
                onShutdown();
              } else if (selectedOption === 'restart') {
                onShutdown(); // For demo, restart also triggers shutdown
              } else {
                onCancel(); // Close programs option
              }
            }}
          >
            OK
          </button>
          <button
            className="shutdown-btn shutdown-btn-cancel"
            onClick={onCancel}
          >
            Cancel
          </button>
          <button
            className="shutdown-btn shutdown-btn-help"
            onClick={() => {
              alert('Windows 95 Help\n\nShut Down: Safely turns off your computer\nRestart: Restarts Windows 95\nClose Programs: Logs off current user\n\nThis is a demo portfolio website.');
            }}
          >
            Help
          </button>
        </div>
      </motion.div>
    </motion.div>
  );
};

export default ShutdownDialog;
