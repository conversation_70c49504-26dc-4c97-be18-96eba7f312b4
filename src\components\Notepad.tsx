import React, { useState } from 'react';
import './Notepad.css';

const Notepad: React.FC = () => {
  const [content, setContent] = useState(`Welcome to Windows 95 Notepad!

This is a simple text editor that mimics the classic Windows 95 Notepad application.

You can type here and experience the nostalgia of the 90s computing era.

Some fun facts about Windows 95:
- Released on August 24, 1995
- Introduced the Start button and taskbar
- First Windows to include Internet Explorer
- Supported long file names (up to 255 characters)
- Came on 13 floppy disks or 1 CD-ROM

Feel free to edit this text and explore the interface!

---
Created with React + TypeScript + Framer Motion
A nostalgic tribute to classic computing`);

  return (
    <div className="notepad-container">
      <textarea
        className="notepad-textarea"
        value={content}
        onChange={(e) => setContent(e.target.value)}
        placeholder="Type your text here..."
        spellCheck={false}
      />
      <div className="notepad-status">
        <span>Line 1, Col 1</span>
        <span>{content.length} characters</span>
      </div>
    </div>
  );
};

export default Notepad;
