import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
// import { soundManager } from '../utils/sounds';
import './BootScreen.css';

interface BootScreenProps {
  onBootComplete: () => void;
}

const BootScreen: React.FC<BootScreenProps> = ({ onBootComplete }) => {
  const [currentStage, setCurrentStage] = useState<'crt' | 'bios' | 'windows' | 'complete'>('crt');
  const [biosText, setBiosText] = useState('');
  const [showCursor, setShowCursor] = useState(true);

  const biosMessages = [
    'BIOS Version 1.0.0',
    'Copyright (C) 1995 Phoenix Technologies Ltd.',
    '',
    'Memory Test: 640K OK',
    'Extended Memory: 7168K OK',
    '',
    'Detecting Primary Master... IDE Hard Disk',
    'Detecting Primary Slave... None',
    'Detecting Secondary Master... ATAPI CD-ROM',
    'Detecting Secondary Slave... None',
    '',
    'Press DEL to enter SETUP',
    '',
    'Loading Windows 95...'
  ];

  useEffect(() => {
    const timers: NodeJS.Timeout[] = [];

    // CRT flicker stage
    if (currentStage === 'crt') {
      timers.push(setTimeout(() => setCurrentStage('bios'), 2000));
    }

    // BIOS stage
    if (currentStage === 'bios') {
      let messageIndex = 0;
      let charIndex = 0;

      const typeMessage = () => {
        if (messageIndex < biosMessages.length) {
          const currentMessage = biosMessages[messageIndex];
          
          if (charIndex <= currentMessage.length) {
            setBiosText(prev => {
              const lines = prev.split('\n');
              lines[lines.length - 1] = currentMessage.substring(0, charIndex);
              return lines.join('\n');
            });
            charIndex++;
            timers.push(setTimeout(typeMessage, 50));
          } else {
            setBiosText(prev => prev + '\n');
            messageIndex++;
            charIndex = 0;
            timers.push(setTimeout(typeMessage, 200));
          }
        } else {
          timers.push(setTimeout(() => setCurrentStage('windows'), 1000));
        }
      };

      setBiosText('');
      timers.push(setTimeout(typeMessage, 500));
    }

    // Windows 95 logo stage
    if (currentStage === 'windows') {
      // Play startup sound
      // soundManager.playStartupSound();
      timers.push(setTimeout(() => setCurrentStage('complete'), 3000));
    }

    // Complete stage
    if (currentStage === 'complete') {
      timers.push(setTimeout(onBootComplete, 1000));
    }

    return () => {
      timers.forEach(timer => clearTimeout(timer));
    };
  }, [currentStage, onBootComplete]);

  // Cursor blinking effect
  useEffect(() => {
    const cursorTimer = setInterval(() => {
      setShowCursor(prev => !prev);
    }, 500);

    return () => clearInterval(cursorTimer);
  }, []);

  return (
    <div className="boot-screen">
      <AnimatePresence mode="wait">
        {currentStage === 'crt' && (
          <motion.div
            key="crt"
            className="crt-stage"
            initial={{ opacity: 0 }}
            animate={{ 
              opacity: [0, 1, 0.8, 1, 0.9, 1],
              scale: [0.98, 1, 0.99, 1]
            }}
            exit={{ opacity: 0 }}
            transition={{ duration: 2, times: [0, 0.1, 0.3, 0.5, 0.8, 1] }}
          >
            <div className="crt-flicker" />
            <div className="scanlines" />
          </motion.div>
        )}

        {currentStage === 'bios' && (
          <motion.div
            key="bios"
            className="bios-stage"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.5 }}
          >
            <div className="bios-content">
              <pre className="bios-text">
                {biosText}
                {showCursor && <span className="cursor">_</span>}
              </pre>
            </div>
            <div className="scanlines" />
          </motion.div>
        )}

        {currentStage === 'windows' && (
          <motion.div
            key="windows"
            className="windows-stage"
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.8, ease: "easeOut" }}
          >
            <div className="windows-logo">
              <div className="logo-container">
                <div className="windows-flag">
                  <div className="flag-red"></div>
                  <div className="flag-green"></div>
                  <div className="flag-blue"></div>
                  <div className="flag-yellow"></div>
                </div>
                <div className="windows-text">Windows 95</div>
              </div>
              <div className="loading-bar">
                <motion.div
                  className="loading-progress"
                  initial={{ width: 0 }}
                  animate={{ width: "100%" }}
                  transition={{ duration: 2.5, ease: "easeInOut" }}
                />
              </div>
            </div>
          </motion.div>
        )}

        {currentStage === 'complete' && (
          <motion.div
            key="complete"
            className="complete-stage"
            initial={{ opacity: 1 }}
            animate={{ opacity: 0 }}
            transition={{ duration: 1 }}
          />
        )}
      </AnimatePresence>
    </div>
  );
};

export default BootScreen;
