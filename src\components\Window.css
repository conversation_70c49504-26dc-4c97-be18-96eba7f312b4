.window {
  position: absolute;
  background: #c0c0c0;
  border: 2px outset #c0c0c0;
  box-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  font-family: 'MS Sans Serif', sans-serif;
  font-size: 11px;
  min-width: 200px;
  min-height: 150px;
  display: flex;
  flex-direction: column;
}

.window-title-bar {
  background: linear-gradient(90deg, #0000ff 0%, #000080 100%);
  color: white;
  padding: 2px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: move;
  user-select: none;
  height: 18px;
  border-bottom: 1px solid #808080;
}

.window-title {
  display: flex;
  align-items: center;
  gap: 4px;
  padding-left: 4px;
  overflow: hidden;
}

.window-icon {
  font-size: 12px;
  line-height: 1;
}

.window-title-text {
  font-weight: bold;
  font-size: 11px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.window-controls {
  display: flex;
  gap: 2px;
  padding-right: 2px;
}

.window-button {
  width: 16px;
  height: 14px;
  background: #c0c0c0;
  border: 1px outset #c0c0c0;
  font-family: 'MS Sans Serif', sans-serif;
  font-size: 9px;
  font-weight: bold;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  color: #000;
}

.window-button:hover {
  background: #d0d0d0;
}

.window-button:active {
  border: 1px inset #c0c0c0;
  background: #a0a0a0;
}

.button-icon {
  line-height: 1;
  font-size: 10px;
}

.minimize-button .button-icon {
  margin-top: 2px;
}

.close-button .button-icon {
  font-size: 12px;
  margin-top: -1px;
}

.window-menu-bar {
  background: #c0c0c0;
  border-bottom: 1px solid #808080;
  display: flex;
  padding: 2px 4px;
  gap: 12px;
  height: 16px;
  align-items: center;
}

.menu-item {
  padding: 2px 6px;
  cursor: pointer;
  font-size: 11px;
  border: 1px solid transparent;
  user-select: none;
}

.menu-item:hover {
  background: #316AC5;
  color: white;
  border: 1px solid #0000ff;
}

.window-content-area {
  flex: 1;
  background: white;
  border: 1px inset #c0c0c0;
  margin: 2px;
  overflow: hidden;
  position: relative;
}

.window-status-bar {
  background: #c0c0c0;
  border-top: 1px solid #808080;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 4px;
  font-size: 10px;
}

.status-text {
  color: #000;
}

.status-resize-grip {
  width: 12px;
  height: 12px;
  background: #c0c0c0;
  border: 1px outset #c0c0c0;
  cursor: nw-resize;
  position: relative;
  margin-right: 2px;
}

.status-resize-grip::before {
  content: '';
  position: absolute;
  top: 2px;
  left: 2px;
  width: 2px;
  height: 2px;
  background: #808080;
  box-shadow: 
    3px 0 0 #808080,
    6px 0 0 #808080,
    0 3px 0 #808080,
    3px 3px 0 #808080,
    6px 3px 0 #808080,
    0 6px 0 #808080,
    3px 6px 0 #808080,
    6px 6px 0 #808080;
}

.status-resize-grip:hover {
  background: #d0d0d0;
}

.status-resize-grip:active {
  border: 1px inset #c0c0c0;
  background: #a0a0a0;
}

/* Focused window styling */
.window:focus-within .window-title-bar {
  background: linear-gradient(90deg, #0000ff 0%, #000080 100%);
}

/* Unfocused window styling */
.window:not(:focus-within) .window-title-bar {
  background: linear-gradient(90deg, #808080 0%, #606060 100%);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .window {
    min-width: 250px;
    min-height: 200px;
  }
  
  .window-title-bar {
    height: 22px;
  }
  
  .window-button {
    width: 20px;
    height: 18px;
  }
  
  .window-menu-bar {
    height: 20px;
  }
  
  .menu-item {
    padding: 3px 8px;
    font-size: 12px;
  }
  
  .window-status-bar {
    height: 22px;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .window {
    border: 3px solid #000000;
  }
  
  .window-title-bar {
    background: #000000;
    color: #ffffff;
    border-bottom: 2px solid #ffffff;
  }
  
  .window-button {
    border: 2px solid #000000;
    background: #ffffff;
    color: #000000;
  }
}
