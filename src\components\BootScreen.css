.boot-screen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: #000;
  z-index: 9999;
  overflow: hidden;
  font-family: 'Courier New', monospace;
}

/* CRT Stage */
.crt-stage {
  width: 100%;
  height: 100%;
  background: #000;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.crt-flicker {
  width: 100%;
  height: 100%;
  background: radial-gradient(ellipse at center, #001100 0%, #000000 70%);
  animation: crtFlicker 0.15s infinite linear alternate;
}

@keyframes crtFlicker {
  0% { opacity: 1; }
  2% { opacity: 0.98; }
  4% { opacity: 0.92; }
  8% { opacity: 1; }
  10% { opacity: 0.96; }
  12% { opacity: 0.98; }
  14% { opacity: 0.94; }
  16% { opacity: 1; }
  18% { opacity: 0.98; }
  20% { opacity: 0.96; }
  32% { opacity: 0.99; }
  34% { opacity: 1; }
  36% { opacity: 0.98; }
  38% { opacity: 0.96; }
  40% { opacity: 1; }
  42% { opacity: 0.99; }
  44% { opacity: 0.98; }
  46% { opacity: 1; }
  56% { opacity: 0.97; }
  58% { opacity: 1; }
  60% { opacity: 0.99; }
  68% { opacity: 1; }
  70% { opacity: 0.99; }
  72% { opacity: 0.98; }
  93% { opacity: 1; }
  95% { opacity: 0.99; }
  97% { opacity: 1; }
  99% { opacity: 0.98; }
  100% { opacity: 1; }
}

/* BIOS Stage */
.bios-stage {
  width: 100%;
  height: 100%;
  background: #000;
  color: #00ff00;
  position: relative;
  padding: 20px;
  box-sizing: border-box;
}

.bios-content {
  width: 100%;
  height: 100%;
  position: relative;
  z-index: 2;
}

.bios-text {
  font-family: 'Courier New', monospace;
  font-size: 14px;
  line-height: 1.2;
  margin: 0;
  white-space: pre-wrap;
}

.cursor {
  background: #00ff00;
  animation: blink 1s infinite;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

/* Windows 95 Stage */
.windows-stage {
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, #008080 0%, #004040 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.windows-logo {
  text-align: center;
  color: white;
}

.logo-container {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 30px;
  gap: 15px;
}

.windows-flag {
  width: 60px;
  height: 60px;
  position: relative;
  transform: rotate(-15deg);
}

.windows-flag > div {
  position: absolute;
  width: 30px;
  height: 30px;
}

.flag-red {
  background: #ff0000;
  top: 0;
  left: 0;
  clip-path: polygon(0 0, 100% 0, 80% 100%, 0 100%);
}

.flag-green {
  background: #00ff00;
  top: 0;
  right: 0;
  clip-path: polygon(20% 0, 100% 0, 100% 100%, 0 100%);
}

.flag-blue {
  background: #0000ff;
  bottom: 0;
  left: 0;
  clip-path: polygon(0 0, 80% 0, 100% 100%, 0 100%);
}

.flag-yellow {
  background: #ffff00;
  bottom: 0;
  right: 0;
  clip-path: polygon(0 0, 100% 0, 100% 100%, 20% 100%);
}

.windows-text {
  font-family: 'Times New Roman', serif;
  font-size: 36px;
  font-weight: bold;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.loading-bar {
  width: 300px;
  height: 20px;
  background: #c0c0c0;
  border: 2px inset #c0c0c0;
  margin: 0 auto;
  position: relative;
  overflow: hidden;
}

.loading-progress {
  height: 100%;
  background: linear-gradient(90deg, #000080 0%, #0000ff 50%, #000080 100%);
  background-size: 20px 100%;
  animation: progressMove 0.5s linear infinite;
}

@keyframes progressMove {
  0% { background-position: 0 0; }
  100% { background-position: 20px 0; }
}

/* Scanlines effect for all stages */
.scanlines {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    transparent 50%, 
    rgba(0, 255, 0, 0.03) 50%
  );
  background-size: 100% 4px;
  pointer-events: none;
  z-index: 1;
}

.complete-stage {
  width: 100%;
  height: 100%;
  background: #000;
}
